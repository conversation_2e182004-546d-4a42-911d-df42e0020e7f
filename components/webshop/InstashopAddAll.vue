<template>
	<BaseWebshopAddToCart v-slot="{onAddToCart, loading}" :data="itemsData" :redirect="false" :add-to-cart-modal="false">
		<div class="instashop-bottom">
			<div class="instashop-total-products">
				<BaseCmsLabel code="instashop_total" :replace="[{'%TOTAL_PRODUCTS%': itemsData.length}]" />
				<!--strong><BaseUtilsFormatCurrency :price="total" /></strong-->
			</div>

			<div class="btn btn-orange btn-instashop-add" :class="{'loading': loading}" @click="onAddToCart"><UiLoader v-if="loading" /><BaseCmsLabel code="add_instashop_to_shopping_cart" tag="span" /></div>
		</div>
		<ul>
			<li v-for="i in itemsData" :key="i.shopping_cart_code">{{i.quantity}}</li>
		</ul>
	</BaseWebshopAddToCart>
</template>

<script setup>
	const props = defineProps(['items', 'model']);
	const itemsData = computed(() => {
		return props.items.map(item => {
			return {
				modalData: item,
				shopping_cart_code: item.shopping_cart_code,
				quantity: item.qty,
			};
		});
	});

	/*
	itemsData mora biti computed property tako da dobiješ apdejtanu količinu. Ovako kako je dolje dobiš samo inicijalni podatak nakon učitavanja i to je to. Nije reactive
	const itemsData = [];
	if(props?.items){
		props.items.forEach(item => {
			if(item?.available_qty > 0){
				console.log(item);
				itemsData.push({modalData: item, quantity: item.qty, shopping_cart_code: item.shopping_cart_code});
			}
		});
	}
	*/
</script>
