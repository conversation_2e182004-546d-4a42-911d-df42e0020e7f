<template>
	<Body class="main-offset-sm page-auth page-edit" />
	<BaseCmsPage v-slot="{page}">
		<AuthMainLayout>
			<template #authContent>
				<ClientOnly>
					<h1 class="a-title" v-if="page?.title">{{page.title}}</h1>
					<BaseAuthEditForm class="form-label auth-form auth-form-edit ajax_siteform_loading" v-slot="{fields, loading, status, values}" :fields-config="{birthday: {type: 'datepicker'}}">
						{{values}}
						<template v-if="status?.data?.errors?.length">
							<div class="global-error" v-for="error in status.data.errors" :key="error">{{ error.field }}: {{ error.error }}</div>
						</template>
						<BaseCmsLabel class="a-edit-subtitle" tag="div" code="personal_data" />
						<div v-if="status?.success" class="global-success"><BaseCmsLabel :code="status.data.label_name" /></div>
						<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel, isTouched, value}">
							<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}, {'field-edit-newsletter': item.name == 'newsletter'}]">
								<!-- FIXME - ne sprema mi se birthday value kad ga odaberem u DatePickeru i spremim -->

								<template v-if="item.name == 'company_name'">
									<BaseCmsLabel class="a-edit-subtitle" tag="span" code="auth_company_data" />
									<span class="company-name-inner">
										<BaseFormInput id="company_name" />
										<label for="company_name">
											<BaseCmsLabel code="company_name" default="company_name" />
										</label>
									</span>
								</template>
								<template v-else>
									<BaseFormInput :id="item.name" />
									<label v-if="item.name != 'birthday'" :for="item.name">
										<BaseCmsLabel :code="item.name == 'location' ? 'zipcode_city' : item.name" :default="item.name" />
										<!-- <template v-if="required && item.name != 'newsletter'">* </template> -->
									</label>
								</template>
								<span class="error" v-show="errorMessage" v-html="errorMessage" />
							</p>
						</BaseFormField>

						<button class="btn btn-auth-submit" type="submit" :class="{'loading': loading}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel code="save" tag="span" /></button>
					</BaseAuthEditForm>
				</ClientOnly>
			</template>
		</AuthMainLayout>
	</BaseCmsPage>
</template>

<script setup>
	function formatDate(date) {
		if (!(date instanceof Date)) return '';
		const day = date.getDate().toString().padStart(2, '0');
		const month = (date.getMonth() + 1).toString().padStart(2, '0');
		const year = date.getFullYear();
		return `${day}.${month}.${year}.`;
	}
</script>

<style scoped lang="less">
	:deep(.field){
		[hidden]{display: none;}
	}
	:deep(.dp__input){
		padding-left: 25px; background-size: 26px; background: url(assets/images/icons/calendar-black.svg) #FFF no-repeat center right +22px;
	}
	:deep(.dp__clear_icon){
		right: 50px;
	}
	.field-company_name{
		.a-edit-subtitle{display: block;}
	}
	.company-name-inner{display: block; position: relative;}
	.field-warehouse_location{
		display: flex; flex-flow: column-reverse; padding-top: 20px; padding-bottom: 35px;
		label{padding-top: 0 !important;}
	}
</style>
