<template>
	<BaseCmsPage v-slot="{page}">
		<Body class="white-bg" />
		<div class="wrapper">
			<ClientOnly>
				<div class="df wrapper qo-wrapper">
					<BaseCatalogQuickOrder v-slot="{items, total, addProduct, removeProduct, addToCartData, loading, valid}">
						<div class="quick-order-col quick-order-col1 qo-col qo-col1">
							<div class="quick-order-cnt" v-if="mobileBreakpoint">
								<BaseCmsLabel code="quick_order_desciption_link" tag="div" />

								<div class="quick-order-total" v-if="valid && addToCartData?.length">
									<div class="quick-order-total-price">
										<span class="label">
											<BaseCmsLabel code="total" />
										</span>
										<span class="value">
											<BaseUtilsFormatCurrency :price="total" />
										</span>
									</div>

									<BaseWebshopAddToCart v-slot="{onAddToCart, loading}" :data="addToCartData">
										<button class="btn btn-terciary btn-icon" :class="{'loading': loading}" @click="onAddToCart"><BaseCmsLabel code="add_to_shopping_cart_all" /></button>
									</BaseWebshopAddToCart>
								</div>
							</div>

							<BaseSearchForm :fetch="searchFormConfig" :submit="false" v-slot="{searchResults, updateValue, searchTerm, loading, handleInput, onBlur, selectedIndex, onReset}">
								<div class="quick-order-form qo-form">
									<div class="quick-order-input-field" :class="{'loading': loading}">
										<label for="quick-order-input"><BaseCmsLabel code="quick_order_top_title" /></label>
										<div class="quick-order-input">
											<span v-if="searchTerm" class="quick-order-input-clear" @click="onReset">x</span>
											<span v-if="loading" class="quick-order-input-loading"><BaseThemeUiLoading /></span>
											<input id="quick-order-input" :placeholder="labels.get('quick_order_input_placeholder')" type="text" :value="searchTerm" @blur="onBlur" @input="updateValue" autocomplete="off" @keyup="handleInput" />
											<CatalogProductAutocomplete :selected-index="selectedIndex" :addProduct="addProduct" v-if="searchResults?.catalogproduct?.length" :items="searchResults.catalogproduct" />
										</div>
										<div class="quick-order-input-help qo-field-tip"><BaseCmsLabel code="quick_order_tip" /></div>
									</div>
								</div>
							</BaseSearchForm>

							<template v-if="loading">
								<BaseThemeUiLoading />
							</template>
							<template v-if="items?.length">
								<div class="quick-order-items">
									<div class="quick-order-items-header qo-row-header active">
										<BaseCmsLabel code="quick_order_products" tag="div" class="qo-row-header-products" />
										<BaseCmsLabel code="quantity" tag="div" class="qo-row-header-qty" />
										<BaseCmsLabel code="total_value" tag="div" class="qo-row-header-total" />
									</div>
									<template v-for="item in items" :key="item.id">
										<BaseCatalogVariations :item="item" v-slot="{items: attributes, selectedVariation, onSelect}" v-model:selectedCode="item.code" v-model:isSelected="item.selectedVariation" v-model:selectedShoppingCartCode="item.shopping_cart_code">
											<div class="wp qo-row quick-order-item">
												<div class="wp-image quick-order-item-col quick-order-item-col1">
													<BaseUiImage :data="item.main_image_thumbs?.['width70-height70-crop1']" default="/images/no-image-50.jpg" />
												</div>
												<div class="wp-content">
													<div class="wp-cnt">
														<div class="wp-title quick-order-item-title">
															<NuxtLink :to="item.url_without_domain">{{ item.title }}</NuxtLink>
														</div>
														<div class="wp-code quick-order-item-code">{{ item.code }}</div>
													</div>
													<div class="wp-qty-container">
														<BaseThemeWebshopQty v-if="(item.variation_quickdata && selectedVariation) || !item.variation_quickdata" :quantity="1" :limit="selectedVariation ? selectedVariation.available_qty : item.available_qty" v-model="item.quantity" />
													</div>
													<div class="wp-total">
														<template v-if="b2b">
															<div class="wp-current-price"><BaseUtilsFormatCurrency :price="item.price_b2b_custom" /></div>
														</template>
														<template v-else>
															<WebshopLoyalty v-slot="{onSubmit, newIsActive, loading, loyalty}">
																<template v-if="loyalty?.active && item.type != 'coupon'">
																	<div class="wp-old-price">
																		<span><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></span>
																	</div>
																	<div class="wp-current-price red" v-if="item.discount_percent > loyalty.discount_percent"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
																	<div class="wp-current-price red" v-else><BaseUtilsFormatCurrency :price="item.loyalty_price" /></div>
																</template>
																<template v-else>
																	<template v-if="item.price_custom > 0">
																		<template v-if="item.discount_percent_custom > 0">
																			<div class="wp-old-price">
																				<span><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></span>
																			</div>
																			<div class="wp-current-price red"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
																		</template>
																		<template v-else>
																			<div class="wp-current-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
																		</template>
																	</template>
																</template>
															</WebshopLoyalty>
														</template>
													</div>
													<div class="wp-btns">
														<span class="wp-btn wp-btn-delete btn-qo-clear active quick-order-item-remove" @click="removeProduct(item)"><BaseCmsLabel code="clear" /></span>
													</div>
												</div>
											</div>
										</BaseCatalogVariations>
									</template>
								</div>
							</template>
						</div>
						<div class="quick-order-col quick-order-col2 qo-col qo-col2" v-interpolation v-if="!mobileBreakpoint">
							<div class="qo-col-body">
								<div class="qo-col-cnt qo-col-cnt-desc" :class="{'active': quickDescription}">
									<BaseCmsRotator :fetch="{code: 'quick_order_desc', limit: 1, response_fields: ['id','title','content']}" v-slot="{items}">
										<div class="qo-col-title" v-html="items[0].title" @click="quickDescription = !quickDescription" />
										<div class="qo-col-desc">
											<div v-html="items[0].content" />
											<BaseCmsLabel code="hide_content" tag="span" class="btn-qo-toggle-desc" @click="quickDescription = !quickDescription" />
										</div>
									</BaseCmsRotator>
								</div>

								<div class="qo-col-cnt qo-col-cnt-totals" :class="{'active': valid && addToCartData?.length}">
									<div class="qo-col-totals">
										<div class="qo-totals-row qo-totals-row-total">
											<BaseCmsLabel code="total" tag="span" class="w-totals-label" />
											<span class="w-totals-value"><BaseUtilsFormatCurrency :price="total" /></span>
										</div>
									</div>

									<BaseWebshopAddToCart v-slot="{onAddToCart, loading}" :data="addToCartData">
										<div class="qo-buttons">
											<button class="btn btn-orange btn-add-to-cart-quick" :class="{'loading': loading}" @click="onAddToCart"><BaseCmsLabel code="add_to_cart_detail" tag="span" /></button>
										</div>
									</BaseWebshopAddToCart>
								</div>
							</div>
						</div>
					</BaseCatalogQuickOrder>
				</div>
				<template #fallback>
					<BaseThemeUiLoading />
				</template>
			</ClientOnly>
		</div>
	</BaseCmsPage>
</template>

<script setup>
	const labels = useLabels();
	const searchFormConfig = {
		'allow_models': ['catalogproduct'],
		'result_per_page': 10,
		'result_image': '60x60_r',
	};

	const quickDescription = ref(false);
</script>

<style lang="less" scoped>
	.qo-wrapper{flex-flow: column-reverse;}
	.quick-order-input{position: relative; display: flex; align-items: center;}
	.quick-order-input-clear{
		position: absolute; z-index: 1; right: 16px; top: auto; font-size: 0; width: 40px; height: 40px; align-items: center; justify-content: center; display: flex;
		&:after{.icon-close2(); font: 16px/1 @fonti; color: @red;}
	}
	.quick-order-input-loading{
		position: absolute; left: 16px; display: flex; align-items: center; justify-content: center; width: 40px; height: 40px;
		.base-loader{width: 100%; height: 100%; background: #fff; z-index: 1;}
	}

	.quick-order-items{position: relative; display: flex; flex-flow: column;}
	.wp-qty-container{
		:deep(.qty-input-container){position: relative; width: 85px; height: 30px;}
		:deep(.qty-input){width: 100%; height: 100%; text-align: center; font-size: 12px; padding: 0 26px;}
		:deep(.qty-btn){
			&:before{width: 12px;}
			&:after{height: 12px; left: 5px;}
		}
	}
</style>
